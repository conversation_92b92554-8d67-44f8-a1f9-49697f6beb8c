"use client";

import React, { useEffect, useState } from "react";
import {
  Box,
  Chip,
  IconButton,
  List,
  ListItemButton,
  ListItemText,
  ListSubheader,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import HelpOutlineIcon from "@mui/icons-material/HelpOutline";
import useHandleMenuNavigation from "@/hooks/useHandleMenuNavigation";
import Routes from "@/constants/routes";
import { usePathname } from "next/navigation";
import { useGeneralStore } from "@/providers/general-store-provider";
import useSectionEngagementAnalytics from "@/components/SideMenu/useSectionEngagementAnalytics";
import { useAuthStore } from "@/stores/auth-store";
import { Flex, Progress } from "antd";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";

// Define the types for individual menu child items
interface MenuChildItem {
  key: string;
  label: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
}

// Define the type for a group of menu items
interface MenuGroup {
  key: string;
  label: string;
  type: "group";
  children: MenuChildItem[];
}

enum Sections {
  home = "Home",
  metrics = "Metrics",
  simulation = "Simulation",
  observability = "Observability",
}

interface SidebarMenuProps {
  selectedKey?: string;
  defaultSelectedKey?: string;
  isMenuCollapsed?: boolean;
  onItemClick?: () => void;
}

export default function SidebarMenu({
  selectedKey,
  defaultSelectedKey,
  isMenuCollapsed = false,
  onItemClick,
}: SidebarMenuProps) {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const { user } = useAuthStore((state) => state);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const collapsed = isMobile ? true : isMenuCollapsed;
  const { currentAgentId } = useGeneralStore((state) => state);
  const pathname = usePathname();
  const navigateTo = useHandleMenuNavigation();
  const sectionEngagementAnalytics = useSectionEngagementAnalytics();

  const menuItems: MenuGroup[] = user?.isAdministrator
    ? [
        {
          key: "adminPanel",
          label: "Admin panel",
          type: "group",
          children: [
            {
              key: "/admin/users",
              label: "Users",
              onClick: () => navigateTo(Routes.users),
            },
          ],
        },
      ]
    : [
        {
          key: Sections.home,
          label: Sections.home,
          type: "group",
          children: [
            {
              key: Routes.home,
              label: (
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                  width="100%"
                >
                  <span>Home</span>
                </Box>
              ),
              onClick: () => navigateTo(Routes.home),
            },
          ],
        },
        {
          key: Sections.metrics,
          label: Sections.metrics,
          type: "group",
          children: [
            {
              key: Routes.metricsMetrics,
              label: (
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                  width="100%"
                >
                  <span>Metrics</span>
                  <Tooltip
                    title="Customize key test performance stats to assess your agent "
                    onOpen={(open) =>
                      open &&
                      generalAnalyticsEvents.trackHelpIconClicked(
                        "Sidebar",
                        "Metrics",
                      )
                    }
                  >
                    <IconButton size="small">
                      <HelpOutlineIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              ),
              onClick: () => navigateTo(Routes.metricsMetrics),
            },
            {
              key: Routes.metricsTestSets,
              label: (
                <Box display="flex" gap={0.5} alignItems="center">
                  <span>Testsets</span>
                  <Chip label="Coming Soon" size="small" />
                </Box>
              ),
              disabled: true,
            },
            {
              key: Routes.metricsLab,
              label: (
                <Box display="flex" gap={0.5} alignItems="center">
                  <span>Lab</span>
                  <Chip label="Coming Soon" size="small" />
                </Box>
              ),
              disabled: true,
            },
          ],
        },
        {
          key: Sections.simulation,
          label: Sections.simulation,
          type: "group",
          children: [
            {
              key: Routes.simulationPersonality,
              label: "Personality",
              onClick: () => {
                sectionEngagementAnalytics.trackPersonalitySectionClicked(
                  currentAgentId,
                );
                navigateTo(Routes.simulationPersonality);
              },
            },
            {
              key: Routes.scenario,
              label: (
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                  width="100%"
                >
                  <span>Evaluator</span>
                  <Tooltip
                    title="Assess bot accuracy and responses"
                    onOpen={(open) =>
                      open &&
                      generalAnalyticsEvents.trackHelpIconClicked(
                        "Sidebar",
                        "Evaluator",
                      )
                    }
                  >
                    <IconButton size="small">
                      <HelpOutlineIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              ),
              onClick: () => {
                sectionEngagementAnalytics.trackEvaluatorSectionClicked(
                  currentAgentId,
                );
                navigateTo(Routes.scenario);
              },
            },
            {
              key: Routes.results,
              label: "Results",
              onClick: () => {
                sectionEngagementAnalytics.trackResultsSectionClicked(
                  currentAgentId,
                );
                navigateTo(Routes.results);
              },
            },
          ],
        },
        {
          key: Sections.observability,
          label: Sections.observability,
          type: "group",
          children: [
            {
              key: Routes.observabilityCalls,
              label: (
                <Box display="flex" gap={0.5} alignItems="center">
                  <span>Calls</span>
                  <Chip label="Coming Soon" size="small" />
                </Box>
              ),
              disabled: true,
            },
            {
              key: Routes.observabilityOverview,
              label: (
                <Box display="flex" gap={0.5} alignItems="center">
                  <span>Overview</span>
                  <Chip label="Coming Soon" size="small" />
                </Box>
              ),
              disabled: true,
            },
          ],
        },
        {
          key: "Account",
          label: "Account",
          type: "group",
          children: [
            ...(user?.call_fact !== undefined && user.call_limit !== undefined
              ? [
                  {
                    key: "creditsLeft",
                    label: (
                      <Flex gap={12} align="center">
                        <Progress
                          percent={
                            user.call_limit
                              ? ((user.call_limit - user.call_fact) /
                                  user.call_limit) *
                                100
                              : 0
                          }
                          type="circle"
                          showInfo={false}
                          size={24}
                        />
                        <span>
                          {user.call_limit - user.call_fact}/{user.call_limit}{" "}
                          calls left
                        </span>
                      </Flex>
                    ),
                    onClick: () => navigateTo(Routes.topUp),
                  },
                ]
              : []),
            {
              key: "top-up",
              label: "Premium",
              onClick: () => {
                sectionEngagementAnalytics.trackTopUpClicked(
                  pathname,
                  user?.call_fact || 0,
                );
                navigateTo(Routes.topUp);
              },
            },
          ],
        },
      ];

  const [activeKeyState, setActiveKeyState] = useState<string>(
    selectedKey || defaultSelectedKey || pathname || "",
  );

  useEffect(() => {
    if (selectedKey && selectedKey !== activeKeyState) {
      setActiveKeyState(selectedKey);
    }
  }, [selectedKey]);

  useEffect(() => {
    if (
      !selectedKey &&
      !defaultSelectedKey &&
      pathname &&
      pathname !== activeKeyState
    ) {
      setActiveKeyState(pathname);
    }
  }, [pathname, selectedKey, defaultSelectedKey]);

  const handleItemClick = (item: MenuChildItem) => {
    sectionEngagementAnalytics.trackSidebarSectionClicked(item.key, pathname);
    if (item.disabled) return;
    setActiveKeyState(item.key);
    if (typeof item.onClick === "function") {
      item.onClick();
    }
    if (onItemClick) {
      onItemClick();
    }
  };

  return (
    <Box
      sx={{
        bgcolor: "#fff",
        height: "100%",
        overflowY: "auto",
      }}
    >
      {menuItems.map((group) => (
        <List
          key={group.key}
          subheader={
            <ListSubheader
              component="div"
              disableSticky
              sx={{ lineHeight: "32px" }}
            >
              {group.label}
            </ListSubheader>
          }
        >
          {group.children.map((child) => (
            <ListItemButton
              key={child.key}
              selected={activeKeyState === child.key}
              onClick={() => handleItemClick(child)}
              disabled={child.disabled}
              sx={
                child.key === "top-up"
                  ? {
                      bgcolor: "#722ed1",
                      "&:hover": { bgcolor: "#722ed1" },
                      mx: 2,
                      p: 0,
                      ...(activeKeyState === child.key && {
                        backgroundColor: "#91caff",
                      }),
                    }
                  : {
                      pl: collapsed ? 2 : 4,
                      ...(activeKeyState === child.key && {
                        backgroundColor: "#91caff",
                      }),
                    }
              }
            >
              {child.key === "top-up" ? (
                <Box sx={{ width: "100%", py: 1, textAlign: "center" }}>
                  <Typography sx={{ color: "#fff" }}>Premium</Typography>
                </Box>
              ) : (
                <ListItemText primary={child.label} />
              )}
            </ListItemButton>
          ))}
        </List>
      ))}
    </Box>
  );
}
