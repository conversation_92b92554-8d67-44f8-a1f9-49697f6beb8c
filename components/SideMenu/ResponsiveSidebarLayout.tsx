"use client";

import React, { ReactNode, useState } from "react";
import { Box, useTheme, useMediaQ<PERSON>y, Drawer } from "@mui/material";
import { Menu } from "./Menu";
import Routes from "@/constants/routes";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useSidebar } from "@/providers/side-menu-context";
import BetaPopover from "../BetaPopover/BetaPopover";
import UpdateAgentModal from "../../app/ui/AgentScreen/components/UpdateAgentModal/UpdateAgentModal";
import LogoBeta from "../Logo-beta/Logo-beta";

interface ResponsiveSidebarProps {
  children: ReactNode;
}

export default function ResponsiveSidebar({
  children,
}: ResponsiveSidebarProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const router = useRouter();
  const [updateAgent, setUpdateAgent] = useState<string | null>(null);

  const { isSidebarOpen, toggleSidebar } = useSidebar();


  if (isMobile) {
    // MOBILE LAYOUT: Use Drawer with context-controlled open/close state.
    return (
      <Box sx={{ display: "flex", width: "100%" }}>
        <Drawer
          anchor="left"
          open={isSidebarOpen}
          onClose={toggleSidebar}
          ModalProps={{ keepMounted: true }}
        >
          <Box sx={{ width: 240, height: "100vh", overflowY: "auto" }}>
            <LogoBeta />
            <Menu onItemClick={toggleSidebar} />
          </Box>
        </Drawer>
        {/* Main content */}
        <Box sx={{ flexGrow: 1, overflowY: "auto", p: 2 }}>{children}</Box>
      </Box>
    );
  }

  // DESKTOP LAYOUT: Permanent sidebar on the left.
  return (
    <Box sx={{ display: "flex", width: "100%" }}>
      <Box
        sx={{
          position: "fixed",
          top: 0,
          left: 0,
          width: 260,
          height: "100vh",
          borderRight: "1px solid #ddd",
          bgcolor: "#fff",
          overflowY: "auto",
        }}
      >
        {/* Logo Area */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: 1,
            py: 2,
            mb: 2,
          }}
        >
          <Image
            src="/logo-black.svg"
            width={120}
            height={60}
            alt="Logo of the application"
            onClick={() => router.push(Routes.scenario)}
            style={{ cursor: "pointer" }}
          />
          <BetaPopover />
        </Box>
        {updateAgent && (
          <UpdateAgentModal
            agentId={updateAgent === "new" ? null : updateAgent}
            isModalOpen={!!updateAgent}
            onCancel={() => setUpdateAgent(null)}
          />
        )}
      </Box>
      {/* Main content area, offset by the sidebar width */}
      <Box sx={{ flexGrow: 1, ml: "260px", overflowY: "auto", height: "100%", pl: 2, pr: 2 }}>
        {children}
      </Box>
    </Box>
  );
}
