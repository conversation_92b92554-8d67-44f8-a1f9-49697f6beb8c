
.custom-audio-container {
    display: flex;
    gap: 5px;
    color: #000;
    padding: 16px;
    border-radius: 20px;
    flex-direction:column;
    box-shadow: none;
    background: #F4F6F8;
    width: 100%;
    position: relative;
  }
  .header {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
  }
  .audio-text .audio-title{
    font-weight: bold;
  }
  .custom-h5-player {
    box-shadow: none;
    background: transparent;
  }

  .rhap_progress-indicator {
    background-color: #191A1D;
  }
  .rhap_button-clear {
    color: #191A1D;
  }