import React from 'react';
import { Box, Typography, But<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@mui/material';
import Image from 'next/image';

interface EmptyStateProps {
  iconSrc: string;
  iconAlt?: string;
  title: string;
  subtitle?: string;
  buttonText?: string;
  onButtonClick?: () => void;
  buttonIcon?: React.ReactNode;
  buttonProps?: React.ComponentProps<typeof MuiButton>;
  minWidth?: number;
  height?: number | string;
  containerSx?: object;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  iconSrc,
  iconAlt = '',
  title,
  subtitle,
  buttonText,
  onButtonClick,
  buttonIcon,
  buttonProps,
  minWidth = 400,
  height = 550,
  containerSx = {},
}) => (
  <Box
    sx={{
      width: '100%',
      height,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      bgcolor: '#fff',
      borderRadius: 4,
      ...containerSx,
    }}
  >
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        bgcolor: '#fff',
        p: 4,
        borderRadius: 4,
        minWidth,
      }}
    >
      {/* Icon */}
      <Box
        sx={{
          bgcolor: '#F4EBFF',
          borderRadius: '16px',
          width: 80,
          height: 80,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mb: 2,
        }}
      >
        <Image src={iconSrc} alt={iconAlt} width={57} height={57} />
      </Box>
      {/* Title */}
      <Typography variant="h6" fontWeight={700} mb={0.5} color="#101828" align="center" sx={{ fontFamily: 'Plus Jakarta Sans' }}>
        {title}
      </Typography>
      {/* Subtitle */}
      {subtitle && (
        <Typography variant="body2" sx={{ fontFamily: 'Plus Jakarta Sans' }} color="#667085" mb={3} align="center">
          {subtitle}
        </Typography>
      )}
      {/* Button */}
      {buttonText && onButtonClick && (
        <MuiButton
          variant="contained"
          sx={{
            bgcolor: '#7F56D9',
            borderRadius: '16px',
            textTransform: 'none',
            fontWeight: 600,
            height: '52px',
            fontSize: 14,
            px: 3,
            width: 225,
            boxShadow: 'none',
            fontFamily: 'Plus Jakarta Sans',
            '&:hover': { bgcolor: '#6941C6 !important'  },
          }}
          startIcon={buttonIcon}
          onClick={onButtonClick}
          {...buttonProps}
        >
          {buttonText}
        </MuiButton>
      )}
    </Box>
  </Box>
);

export default EmptyState; 