import { Box } from "@mui/material";
import ChatBubble, { SegmentData } from "../ChatBubble/ChatBubble";
interface ChatBubbleListProps {
    segments: SegmentData[];
}

function mapSegmentToChatProps(segment: SegmentData) {
    const [timePart] = segment.add_to_history_time.split(".");
    const datePart = ""; 

    return {
        id: segment.item_id,
        name: segment.speaker === "bot" ? "Bot" : "User",
        role: segment.speaker,
        time: timePart,
        date: datePart,
        message: segment.transcript,
    };
}
export default function ChatBubbleList({ segments }: ChatBubbleListProps) {
    return (
        <Box sx={{ width: "100%", backgroundColor: "#fff" }}>
            {segments && segments.map((seg) => {
                const bubbleProps = mapSegmentToChatProps(seg);
                return <ChatBubble key={seg.item_id} {...bubbleProps} />;
            })}
        </Box>
    );
}