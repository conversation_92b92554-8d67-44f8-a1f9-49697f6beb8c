"use client";

import React, { useEffect, useState, useRef } from "react";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import styles from './TestimonialCarousel.module.scss';

interface Testimonial {
  backgroundColor: string;
  id: string;
  name: string;
  role: string;
  company?: string;
  photoUrl: string;
  text: string;
}

interface LogoItem {
  id: string;
  width: number;
  height: number;
  src: string;
  alt: string;
}

interface TestimonialCarouselProps {
  testimonials: Testimonial[];
  logos: LogoItem[];
  autoSwitchInterval?: number;
}

export default function TestimonialCarousel({
  testimonials,
  logos,
  autoSwitchInterval = 5000,
}: TestimonialCarouselProps) {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [, setIsAnimating] = useState(true);
  const animationRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!testimonials || testimonials.length === 0) {
      generalAnalyticsEvents.trackEmptyStateEncountered(
        "No testimonials provided",
      );
    }

    const startAnimation = () => {
      setIsAnimating(true);
      animationRef.current = setTimeout(() => {
        setCurrentIndex((prev) => (prev + 1) % testimonials.length);
        startAnimation();
      }, autoSwitchInterval);
    };

    startAnimation();

    return () => {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    };
  }, [testimonials, autoSwitchInterval]);

  const handleIndicatorClick = (idx: number) => {
    setIsAnimating(false);
    if (animationRef.current) {
      clearTimeout(animationRef.current);
    }
    setCurrentIndex(idx);
    
    setTimeout(() => {
      setIsAnimating(true);
      animationRef.current = setTimeout(() => {
        setCurrentIndex((prev) => (prev + 1) % testimonials.length);
      }, autoSwitchInterval);
    }, 100);
  };

  if (!testimonials || testimonials.length === 0) {
    return <Typography>No testimonials found</Typography>;
  }

  const currentTestimonial = testimonials[currentIndex];

  return (
    <Box className={styles.testimonialCarousel}>
      <Box className={styles.indicatorContainer}>
        {testimonials.map((_, idx) => (
          <Box
            key={idx}
            onClick={() => handleIndicatorClick(idx)}
            className={`${styles.indicator} ${idx === currentIndex ? styles['indicator--active'] : styles['indicator--inactive']}`}
          />
        ))}
      </Box>

      <Box 
        className={styles.testimonialCard}
        sx={{ backgroundColor: currentTestimonial.backgroundColor }}
      >
        <Box className={styles.testimonialCard__content}>
          <Box className={styles.testimonialCard__imageContainer}>
            <Box className={styles.testimonialCard__imageWrapper}>
              <Image
                src={currentTestimonial.photoUrl}
                alt={currentTestimonial.name}
                layout="fill"
                objectFit="contain"
              />
            </Box>
          </Box>

          <Typography 
            variant="body1"
            className={styles.testimonialCard__quote}
          >
            {currentTestimonial.text}
          </Typography>

          <Box className={styles.testimonialCard__author}>
            <Typography 
              variant="h6"
              className={styles.testimonialCard__name}
            >
              {currentTestimonial.name}
            </Typography>
            <Typography 
              variant="body2"
              className={styles.testimonialCard__role}
            >
              {currentTestimonial.role}
              {currentTestimonial.company && (
                <span className={styles['testimonialCard__role--company']}>
                  {` @${currentTestimonial.company}`}
                </span>
              )}
            </Typography>
          </Box>
        </Box>
      </Box>

      <Box className={styles.logosSection}>
        <Box>
          <Typography 
            variant="body1"
            className={styles.logosTitle}
          >
            We tested ai voice agents from
          </Typography>
        </Box>
        <Box className={styles.logosContainer}>
          {logos.map((logo) => (
            <div 
              key={logo.id} 
              className={styles.logoItem}
              style={{ width: logo.width, height: logo.height }}
            >
              <Image
                src={logo.src}
                alt={logo.alt}
                layout="fill"
                objectFit="contain"
              />
            </div>
          ))}
        </Box>
      </Box>
    </Box>
  );
}
