"use client";

import React from "react";
import {
    Dialog,
    DialogTitle,
    DialogContent,
    <PERSON>alogActions,
    Button,
    Typography,
    Box,
} from "@mui/material";

const termsContent = `
Thank you for your interest in CNTXT FZCO (“CNTXT,” “we,” “us,” or “our”) and our website, www.cntxt.tech, as well as any related websites (collectively referred to as the “Site”). These Terms and Conditions (“Terms”), along with CNTXT’s Privacy Policy, govern your access to and use of the Site. Please review these Terms carefully. These Terms do not govern your use of CNTXT’s services, which are subject to the CNTXT General Terms and Conditions for Services or any other written agreement between you and CNTXT.

BY CLICKING “AGREE” OR BY ACCESSING OR USING THE SITE IN ANY MANNER, YOU CONFIRM THAT YOU READ AND UNDERSTOOD THESE TERMS, AND AGREE TO BE LEGALLY BOUND BY THEM, AS WELL AS BY ALL APPLICABLE LAWS AND REGULATIONS. If you represent an entity, you warrant that you have the authority to bind that entity to these Terms. If you do not accept these Terms, you must not access or use the Site. Throughout the Terms, “we,” “us,” “our” and “ours” refer to CNTXT, and “you,” “your” or “yours” refer to you personally (i.e., the individual who reads and agrees to be bound by these Terms) and, if you access the Sites on behalf of a person or a legal entity, to that entity or the person.

Intellectual Property Ownership—All data, text, content, documents, names, logos, trademarks, service marks, brand identities, pictures, graphics, characters, trade names, designs, copyrights, trade dress, or other intellectual property appearing in the Website, and the organization, compilation, look and feel, illustrations, artwork, videos, music, software and other works on the Site (the “Materials”) are the property of CNTXT and its affiliates or are utilized with authorization or under a license from a third party (collectively referred to as the “Owner”) and are safeguarded by copyright, trademark, and other intellectual property and proprietary rights laws. As between CNTXT and you, all right, title and interest in and to the Materials will at all times remain with CNTXT and/or its Owners. The word “CNTXT”, “CNTXT” and the “CNTXT” logos, and other marks, logos and titles are registered and/or common law trade names, trademarks or service marks of CNTXT. CNTXT reserves all other rights. Except as expressly provided herein, nothing on the Site shall be construed as conferring any license under CNTXT’s and/or its Owner’s intellectual property rights, whether by estoppel, implication or otherwise. Notwithstanding anything herein to the contrary, CNTXT may revoke any of the foregoing rights and/or your access to the Site, or any part thereof, at any time without prior notice.

Use License- Subject to your complete and ongoing compliance with these Terms, CNTXT hereby grants you a non-exclusive, non-transferable, non-sublicensable, revocable, worldwide right to (a) access and use the Site, solely with supported browsers through the Internet for your own internal purposes. You are prohibited from allowing the Site to be used by or on behalf of unauthorized third parties. These Terms do not grant you any rights to transfer or assign access or usage rights to the Site. All rights not expressly granted to you are reserved by CNTXT and its licensors. You are not permitted to (i) alter or create derivative works from the Sites; (ii) reverse engineer the Sites or access them in order to (a) develop a competing product or service, (b) create a product with similar features, functions, or graphics as the Sites, or (c) replicate any features, functions, or graphics of the Sites. Furthermore, you acknowledge and agree that CNTXT retains exclusive ownership of all rights, title, and interest in and to the Sites, including but not limited to the visual interfaces, graphics, design, compilation, data, computer code (both source and object code), products, software, services, and all other elements of the Site, as well as all associated intellectual property rights.

Registration- If you have created an account through the Sites (an “Account”), where applicable, you are solely responsible for keeping your Account information, including your password, confidential and for all activities that take place under your Account. You agree to inform CNTXT immediately if you become aware of any unauthorized use of your Account or password, or any other security breach. However, you will still be liable for any losses suffered by CNTXT or others due to your intentional or unintentional allowance of unauthorized access to your Account or Account information. You are prohibited from using another person’s ID, password, or Account unless explicitly authorized by CNTXT or permitted under a Services Agreement. CNTXT is not responsible for any loss or damage resulting from your failure to adhere to these responsibilities.

Information Submissions Through or To Our Sites- At CNTXT's sole discretion, you may be allowed to submit any kind of information ("Submissions") through the Sites (e.g., via forms). "Submissions" include messages, contact details, personal information, emails, text, graphics, code, questions, suggestions, comments, feedback, ideas, plans, notes, drawings, sample data, audio, images, video, creative materials, and other content that you may post or transmit on forums, blogs, or other interactive features of the Sites. Unless otherwise agreed to by you and CNTXT, any Submission you upload, email, post, or transmit will be considered non-confidential. By doing so, you automatically grant (or warrant that the owner of such rights has expressly granted) to CNTXT an irrevocable, perpetual, unrestricted, non-exclusive, worldwide, fully paid-up, sub-licensable, and royalty-free license to use, make, have made, offer for sale, sell, copy, distribute, perform, display (whether publicly or otherwise), modify, adapt, publish, transmit and otherwise exploit such Submission, by means of any form, medium, or technology now known or later developed, and to grant to others rights to do any of the foregoing to the extent permitted by law. Additionally, you confirm that any moral rights associated with the Submission have been waived. For each Submission, you represent and guarantee that you possess all necessary rights to grant the license described above, and that the Submission, as well as its provision to and through the Sites, does not infringe upon the privacy, publicity, contractual, intellectual property, or any other rights of any individual or entity, nor does it violate any applicable laws, regulations, or rules. You acknowledge that CNTXT may have documents, information, ideas or any kind of materials already under consideration or development that are or may be similar to your Submissions and that you are not entitled to any form of compensation or reimbursement from CNTXT in connection with your Submissions. You agree to be fully responsible for, and cover any royalties, fees, damages, or other payments owed to any individual or organization due to any Submission you make to the Sites.

... (the remaining terms go here)
`;

interface TermsDialogProps {
    open: boolean;
    onClose: () => void;
}

export default function TermsDialog({ open, onClose }: TermsDialogProps) {
    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth="md"
            fullWidth
            PaperProps={{
                sx: {
                    borderRadius: 2,
                    p: 2,
                },
            }}
        >
            <DialogTitle>
                <Typography variant="h6" fontWeight="bold">
                    Terms &amp; Conditions
                </Typography>
            </DialogTitle>
            <DialogContent dividers sx={{ maxHeight: 500 }}>
                <Box sx={{ whiteSpace: "pre-wrap" }}>
                    <Typography variant="body2" sx={{ color: "#333", lineHeight: 1.6 }}>
                        {termsContent}
                    </Typography>
                </Box>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose} variant="contained" color="primary">
                    Agree
                </Button>
            </DialogActions>
        </Dialog>
    );
}
