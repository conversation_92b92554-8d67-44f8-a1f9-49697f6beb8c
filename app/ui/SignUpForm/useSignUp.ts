import { useState } from "react";
import { usePathname } from "next/navigation";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import { IUserDto } from "@/app/api/types/userDto";
import useLogin from "@/hooks/useLogin";

export const useSignUp = () => {
  const analytics = useRudderStackAnalytics();
  // const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  // const router = useRouter();
  const notify = useNotification();
  const [loading, setLoading] = useState(false);
  // const searchParams = useSearchParams();
  const pathname = usePathname();
  const { loginUser } = useLogin();

  interface ISignUpForm {
    email: string;
    password: string;
  }

  const onFinish = async (values: ISignUpForm) => {
    analytics?.track("Sign Up", {
      email: values?.email,
      domain: values?.email.split("@")[1],
      app: "test.ai",
      page_name: pathname,
    });
    setLoading(true);
    // const signupSource = searchParams.get("signup_source") || document.referrer || "unknown";
    
    try {
      const response = await fetch("/api/signup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: values.email,
          password: values.password,
          first_name: "TEST AI",
          last_name: "",
          company_name: "TEST AI",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        notify.error({
          message: "Signup Failed",
          description: errorData.message || "Something went wrong. Please try again later.",
        });
        return;
      }

      const signUpData: IUserDto = await response.json();

      analytics?.track("Signup_Completed", {
        email: values?.email,
        domain: values?.email.split("@")[1],
        user_id: signUpData.id,
        app: "test.ai",
        marketing_campaign_id: "",
        signup_method: "email",
        page_name: pathname,
      });

      notify.success({
        message: "Registration Successful",
        description: "You have been successfully registered and logged in.",
        duration: 3,
      });
      await new Promise(resolve => setTimeout(resolve, 2000));
      await loginUser({ email: values.email, password: values.password });
      
    } catch (e) {
      console.error("Signup error:", e);
      notify.error({
        message: "Unexpected Error",
        description: "Something went wrong. Please try again later.",
      });
    } finally {
      setLoading(false);
    }
  };

  return { onFinish, loading };
};