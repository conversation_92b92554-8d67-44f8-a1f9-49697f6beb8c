'use client';

import React, { useState, useEffect } from 'react';
import { Box, TextField, Button, Typography, Stack, InputAdornment, Link, IconButton } from '@mui/material';
import Image from 'next/image';
import { useSignUp } from './useSignUp';
import useRudderStackAnalytics from '@/hooks/useRudderAnalytics';
import { usePathname, useSearchParams } from 'next/navigation';
import styles from './../LoginForm/auth.module.scss';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import TermsDialog from '@/components/TermsDialog/TermsDialog';
import PageTransition from '../PageTransition/PageTransition';

interface ISignUpForm {
  email: string;
  password: string;
  name: string;
  company_name: string;
}

export default function SignUpForm() {
  const analytics = useRudderStackAnalytics();
  const { onFinish, loading } = useSignUp();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const [formValues, setFormValues] = useState<ISignUpForm>({
    email: '',
    password: '',
    name: 'TEST AI',
    company_name: 'TEST AI',
  });

  const [isSubmittable, setIsSubmittable] = useState<boolean>(false);
  const [hasSignedUp, setHasSignedUp] = useState<boolean>(false);
  const [showGmailHint, setShowGmailHint] = useState<boolean>(false);
  const [openTerms, setOpenTerms] = useState(false);

  useEffect(() => {
    return () => {
      if (!hasSignedUp) {
        const signupSource = searchParams.get('signup_source') || document.referrer || 'unknown';
        analytics?.track('Signup_Attempted', {
          app: 'test.ai',
          signup_source: signupSource,
          device: navigator.userAgent,
          page_name: pathname,
        });
      }
    };
  }, [hasSignedUp, analytics, searchParams]);

  useEffect(() => {
    const { email, password, name, company_name } = formValues;
    const isValidEmail = email.includes('@') && email.trim().length > 0;
    const isValidPassword = password.trim().length > 0;
    const isValidName = name.trim().length >= 3;
    const isValidCompany = company_name.trim().length >= 3;
    setIsSubmittable(isValidEmail && isValidPassword && isValidName && isValidCompany);
  }, [formValues]);

  const handleInputChange = (field: keyof ISignUpForm, value: string) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
    if (field === 'email') {
      setShowGmailHint(value.toLowerCase().endsWith('@gmail.com'));
    }
  };

  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (isSubmittable) {
      setHasSignedUp(true);
      onFinish(formValues);
    }
  };

  const handleChange = (field: keyof ISignUpForm) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormValues((prev) => ({ ...prev, [field]: e.target.value }));
  };

  const inputStyles = {
     '&&& .Mui-focused': {
      border: '1px solid #DEB9FF',
      borderRadius: '16px',
      background: '#fff',
    },
    '& .MuiOutlinedInput-root': {
      borderRadius: '16px',
      padding: '0 16px',
      height: '56px',
      backgroundColor: '#fff',
      boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
      '& fieldset': {
        border: '1px solid #D0D5DD',
        borderRadius: '16px',
      },
      '&:hover fieldset': {
        borderColor: '#D0D5DD',
      },
      '&.Mui-focused fieldset': {
        borderColor: '#D0D5DD',
        borderWidth: '1px',
      },
      '&:hover': {
        border: '1px solid #D0D5DD',
        background: '#F7F7F7',
      },
    },
    '& .MuiInputBase-input': {
      fontSize: '16px',
      lineHeight: '24px',
      fontFamily: 'Plus Jakarta Sans',
      '&::placeholder': {
        color: '#667085',
        opacity: 1,
      },
    },
    '& .MuiInputAdornment-root': {
      marginRight: '8px',
    },
  };

  return (
    <PageTransition>
      <Box className={styles.section}>
        <Box className={styles.leftSide}>
          <Box className={styles.formWrapper}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignContent: 'center',
                mt: '33px',
              }}
            >
              <Image
                src='/logo-black.svg'
                alt='Logo of the application'
                width={131}
                height={28}
                className={styles.logo}
              />
            </Box>
            <Box
              component='form'
              onSubmit={handleSubmit}
              sx={{
                borderRadius: 2,
                width: '100%',
                flexShrink: 0,
              }}
            >
              <Stack spacing={2} sx={{ width: '100%', flexShrink: 0 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography
                    sx={{
                      fontSize: '28px',
                      fontFamily: 'Plus Jakarta Sans',
                      fontWeight: 800,
                      lineHeight: '38px',
                      mb: '12px',
                      letterSpacing: '0px',
                    }}
                  >
                    Stop Guessing if Your VoiceAgent Works. Know It!
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: '16px',
                      color: '#1B1B1BC',
                      fontFamily: 'Plus Jakarta Sans',
                      fontWeight: 500,
                      lineHeight: '24px',
                      mb: '32px',
                      letterSpacing: '0px',
                    }}
                  >
                    Easily manage your autonomous voice testing assistants all in one dashboard.
                  </Typography>
                </Box>
                <Box sx={{ mb: '24px' }}>
                  <Typography
                    variant='body2'
                    sx={{
                      mb: 1,
                      fontWeight: 500,
                      fontSize: '14px',
                      lineHeight: '20px',
                      color: '#344054',
                      fontFamily: 'Plus Jakarta Sans',
                    }}
                  >
                    Email
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder='Enter your email'
                    variant='outlined'
                    value={formValues.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    sx={inputStyles}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position='start'>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Image
                              src='/forms/email-logo.svg'
                              alt='Email icon'
                              width={24}
                              height={24}
                              style={{ opacity: 0.7 }}
                            />
                          </Box>
                        </InputAdornment>
                      ),
                    }}
                    helperText={
                      showGmailHint ? (
                        <Typography
                          sx={{
                            color: '#475467',
                            fontSize: '14px',
                            fontFamily: 'Plus Jakarta Sans',
                            mt: '6px',
                          }}
                        >
                          Consider using your company email instead of Gmail.
                        </Typography>
                      ) : (
                        ''
                      )
                    }
                  />
                </Box>

                <Box>
                  <Typography
                    variant='body2'
                    sx={{
                      mb: 1,
                      fontWeight: 500,
                      fontSize: '14px',
                      lineHeight: '20px',
                      color: '#344054',
                      fontFamily: 'Plus Jakarta Sans',
                    }}
                  >
                    Password
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder='Enter your password'
                    type={showPassword ? 'text' : 'password'}
                    value={formValues.password}
                    onChange={handleChange('password')}
                    variant='outlined'
                    sx={inputStyles}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position='start'>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Image
                              src='/forms/lock.svg'
                              alt='Password icon'
                              width={24}
                              height={24}
                              style={{ opacity: 0.7 }}
                            />
                          </Box>
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position='end'>
                          <IconButton onClick={togglePasswordVisibility} edge='end' sx={{ color: '#667085' }}>
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                </Box>

                <Button
                  type='submit'
                  variant='contained'
                  fullWidth
                  disabled={!isSubmittable || loading}
                  sx={{
                    mt: '24px',
                    height: '52px',
                    borderRadius: '16px',
                    textTransform: 'none',
                    fontSize: '16px',
                    fontWeight: 600,
                    lineHeight: '24px',
                    fontFamily: 'Plus Jakarta Sans',
                    boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
                    backgroundColor: '#7F56D9',
                    '&:hover': {
                      backgroundColor: '#6941C6',
                    },
                    '&:disabled': {
                      backgroundColor: '#F2F2F2',
                      color: '#1B1B1B',
                    },
                  }}
                >
                  Sign up
                </Button>

                <Box>
                  <Typography
                    sx={{
                      color: '#475467',
                      fontSize: '14px',
                      lineHeight: '20px',
                      textDecoration: 'none',
                      fontFamily: 'Plus Jakarta Sans',
                      fontWeight: 400,
                    }}
                  >
                    By signing up you are accepting the Terms &amp; Conditions and Privacy Policy.
                  </Typography>
                </Box>

                <Box sx={{ textAlign: 'center', mt: 1 }}>
                  <Typography
                    sx={{
                      color: '#1B1B1B',
                      fontWeight: 600,
                      fontSize: '14px',
                      lineHeight: '20px',
                      fontFamily: 'Plus Jakarta Sans',
                    }}
                  >
                    Already have an account?{' '}
                    <Link
                      href='/login'
                      sx={{
                        fontWeight: 600,
                        fontSize: '14px',
                        lineHeight: '20px',
                        fontFamily: 'Plus Jakarta Sans',
                        marginLeft: '10px',
                        color: '#915EFF',
                        display: 'inline-flex',
                        alignItems: 'center',
                        textDecoration: 'none',
                        '&:hover': {
                          textDecoration: 'underline',
                        },
                      }}
                    >
                      Sign in
                      <Image src='/chevron-right.svg' alt='' width={16} height={16} />
                    </Link>
                  </Typography>
                </Box>
              </Stack>
            </Box>

            <Box sx={{ textAlign: 'center', mt: 2 }}>
              <Typography>
                <Link
                  onClick={() => setOpenTerms(true)}
                  sx={{
                    color: '#475467',
                    cursor: 'pointer',
                    fontWeight: 500,
                    fontFamily: 'Plus Jakarta Sans',
                    lineHeight: '20px',
                    textDecoration: 'none',
                  }}
                >
                  Terms & Conditions
                </Link>
              </Typography>
            </Box>
          </Box>
        </Box>
        <TermsDialog open={openTerms} onClose={() => setOpenTerms(false)} />
      </Box>
    </PageTransition>
  );
}
