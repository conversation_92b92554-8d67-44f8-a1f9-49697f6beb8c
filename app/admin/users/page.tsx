"use client";

import {
  Button,
  ConfigProvider,
  Dropdown,
  Flex,
  TableColumnsType,
  Tag,
} from "antd";
import styles from "./styles.module.scss";
import cn from "classnames";
import { usersTableTheme } from "./constants";
import { Table } from "@/components/Table/Table";
import { useGeneralStore } from "@/providers/general-store-provider";
import { useEffect, useState } from "react";
import { IUser } from "@/types/user";
import UsersModal from "./components/UsersModal/UsersModal";
import { useAuthStore } from "@/stores/auth-store";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";

export default function UsersPage() {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const { users, setUsers } = useGeneralStore((state) => state);
  const { user } = useAuthStore((state) => state);
  const [createUserOpen, setCreatUserOpen] = useState(false);
  const [userToEdit, setUserToEdit] = useState<IUser | null>(null);
  const [loading, setLoading] = useState(true);
  const notify = useNotification();

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        const response = await fetch("/api/admin/getUsers", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${user?.token || ""}`,
          },
        });

        if (!response.ok) {
          console.error(`HTTP error! status: ${response.status}`);
          notify.error({
            message: "Failed to get users",
            description: `HTTP error! status: ${response.status}`,
          });
          generalAnalyticsEvents.trackApiRequestFailed(
            "GET /users",
            response.status.toString(),
            `Failed to get users`,
          );
          return;
        }

        const responseBody = await response.json();
        setUsers(responseBody.users);
      } catch (error) {
        console.error("Error fetching users:", error);
        notify.error({
          message: "Unexpected Error",
          description: "Failed to load users.",
        });
        generalAnalyticsEvents.trackApiRequestFailed(
          "GET /users",
          "500",
          `Unexpected Error: ${error}`,
        );
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: TableColumnsType<IUser> = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      render: (id: string) => <b className={styles.id}>{id}</b>,
      width: 100,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "First name",
      dataIndex: "first_name",
      key: "first_name",
    },
    {
      title: "Last name",
      dataIndex: "last_name",
      key: "last_name",
    },
    {
      title: "Company",
      dataIndex: "company_name",
      key: "company_name",
    },
    {
      title: "Active",
      dataIndex: "active",
      key: "active",
      render: (active: boolean) =>
        active ? (
          <Tag color="success">Active</Tag>
        ) : (
          <Tag color="error">Inactive</Tag>
        ),
    },
    {
      title: "Call Fact",
      dataIndex: "call_fact",
      key: "call_fact",
    },
    {
      title: "Call Limit",
      dataIndex: "call_limit",
      key: "call_limit",
    },
    {
      title: "Action",
      key: "action",
      render: (_, item) => (
        <Dropdown
          trigger={["click"]}
          rootClassName={styles.dropdown}
          menu={{
            items: [
              {
                key: "title",
                label: <b className={styles.dropdownTitle}>Actions</b>,
                type: "group",
                children: [
                  { type: "divider" },
                  {
                    className: styles.dropdownItem,
                    key: "edit",
                    label: (
                      <Button
                        variant="link"
                        color="default"
                        icon={<i className={cn(styles.bx, "bx bx-edit")}></i>}
                      >
                        Edit
                      </Button>
                    ),
                    onClick: () => {
                      setUserToEdit(item);
                      setCreatUserOpen(true);
                    },
                  },
                  // {
                  //   className: styles.dropdownItem,
                  //   key: "delete",
                  //   label: (
                  //     <Button
                  //       variant="link"
                  //       color="danger"
                  //       icon={<i className={cn(styles.bx, "bx bx-trash")}></i>}
                  //     >
                  //       Delete
                  //     </Button>
                  //   ),
                  //   onClick: () => {
                  //     deleteUser(item.id);
                  //   },
                  // },
                ],
              },
            ],
          }}
        >
          <Button
            shape="default"
            type="text"
            icon={
              <i className={cn(styles.bx, "bx bx-dots-horizontal-rounded")}></i>
            }
          />
        </Dropdown>
      ),
      width: 104,
    },
  ];

  return (
    <Flex vertical gap={24}>
      {/*<Button*/}
      {/*  size="large"*/}
      {/*  icon={<i className={cn(styles.bx, "bx bx-plus")}></i>}*/}
      {/*  color="default"*/}
      {/*  variant="solid"*/}
      {/*  className={cn(styles.button, styles.addButton)}*/}
      {/*  onClick={() => setCreatUserOpen(true)}*/}
      {/*>*/}
      {/*  Create User*/}
      {/*</Button>*/}
      <ConfigProvider theme={usersTableTheme}>
        <Table<IUser>
          columns={columns}
          dataSource={users}
          rowKey={(record) => record.id}
          loading={loading}
        />
      </ConfigProvider>

      {createUserOpen && (
        <UsersModal
          onCancel={() => setCreatUserOpen(false)}
          isModalOpen={createUserOpen}
          initialData={userToEdit}
        />
      )}
    </Flex>
  );
}
