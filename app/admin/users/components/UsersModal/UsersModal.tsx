import {
  <PERSON><PERSON>,
  ConfigP<PERSON>ider,
  Flex,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
} from "antd";
import cn from "classnames";
import styles from "./UsersModal.module.scss";
import { useGeneralStore } from "@/providers/general-store-provider";
import { generateScenariosModalTheme } from "@/app/(dashboard)/scenarios/components/constants";
import { IUser } from "@/types/user";
import { useAuthStore } from "@/stores/auth-store";
import { useEffect, useState } from "react";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import { IErrorFields, IValidateError } from "@/types/validateError";
import { useDebounce } from "react-use";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";

interface IUserModalProps {
  onCancel: () => void;
  isModalOpen: boolean;
  initialData: IUser | null;
}

const UsersModal = ({
  onCancel,
  isModalOpen,
  initialData,
}: IUserModalProps) => {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const { addUser, editUser } = useGeneralStore((state) => state);
  const { user } = useAuthStore((state) => state);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm<IUser>();
  const notify = useNotification();

  const [submittable, setSubmittable] = useState<boolean>(false);
  const values = Form.useWatch([], form);
  const [validationErrors, setValidationErrors] = useState<IErrorFields[]>([]);

  useDebounce(
    () => {
      validationErrors.forEach((fieldError: IErrorFields) => {
        if (
          form.isFieldTouched(fieldError.name[0] as keyof Omit<IUser, "id">)
        ) {
          generalAnalyticsEvents.trackFormValidationError(
            "UsersModal",
            fieldError.name[0],
            fieldError.errors[0],
          );
        }
      });
    },
    2000,
    [validationErrors],
  );

  useEffect(() => {
    if (initialData) {
      setSubmittable(true);
      return;
    }

    form
      .validateFields({ validateOnly: true })
      .then(() => {
        setValidationErrors([]);
        setSubmittable(true);
      })
      .catch((reason: IValidateError) => {
        setValidationErrors(reason.errorFields);
        if (reason.errorFields.length > 0) {
          setSubmittable(false);
        } else {
          setSubmittable(true);
        }
      });
  }, [form, values]);

  const handleSubmit = async () => {
    const values = form.getFieldsValue();
    if (initialData) {
      try {
        setLoading(true);
        const response = await fetch("/api/admin/editUser", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${user?.token || ""}`,
          },
          body: JSON.stringify({
            ...initialData,
            first_name: values.first_name,
            last_name: values.last_name,
            email: values.email,
            call_limit: values.call_limit,
            company_name: values.company_name,
            active: values.active,
          }),
        });

        if (!response.ok) {
          console.error(`HTTP error! status: ${response.status}`);
          notify.error({
            message: "Failed to edit users",
            description: `HTTP error! status: ${response.status}`,
          });
          generalAnalyticsEvents.trackApiRequestFailed(
            "PUT /users",
            response.status.toString(),
            `Failed to edit users`,
          );
          return;
        }

        // const responseBody = await response.json();
        editUser({
          ...initialData,
          ...values,
        });
      } catch (error) {
        console.error("Error editing users:", error);
        notify.error({
          message: "Unexpected Error",
          description: "Failed to edit users.",
        });
        generalAnalyticsEvents.trackApiRequestFailed(
          "PUT /users",
          "500",
          `Unexpected Error: ${error}`,
        );
      } finally {
        setLoading(false);
      }
    } else {
      addUser({
        ...values,
      });
    }

    onCancel();
  };

  return (
    <ConfigProvider theme={generateScenariosModalTheme}>
      <Modal
        centered
        title={initialData ? "Update User" : "Create User"}
        open={isModalOpen}
        onCancel={onCancel}
        rootClassName={styles.modalRoot}
        footer={null}
        closeIcon={<i className={cn(styles.bx, "bx bx-x")}></i>}
      >
        <Form
          layout="vertical"
          requiredMark={false}
          initialValues={
            initialData || {
              first_name: "",
              last_name: "",
              email: "",
              isAdministrator: false,
            }
          }
          form={form}
          disabled={loading}
        >
          <Form.Item
            required
            label={
              <Flex gap={8}>
                <span>First Name*</span>
              </Flex>
            }
            name="first_name"
            rules={[
              {
                required: true,
                message: "First name is required",
              },
            ]}
          >
            <Input placeholder="First name" type="text" name="first_name" />
          </Form.Item>
          <Form.Item required label={<span>Last Name*</span>} name="last_name">
            <Input placeholder="User last name" type="text" name="last_name" />
          </Form.Item>
          <Form.Item
            required
            label={<span>Email*</span>}
            name="email"
            rules={[
              {
                type: "email",
                required: true,
                message: "Please input user email!",
              },
            ]}
          >
            <Input placeholder="Email" type="email" name="email" />
          </Form.Item>
          <Form.Item
            required
            label={<span>Company Name*</span>}
            name="company_name"
            rules={[
              {
                required: true,
                message: "Please input company name!",
              },
            ]}
          >
            <Input placeholder="Company name" type="text" name="company_name" />
          </Form.Item>
          <Flex gap={16}>
            <Form.Item
              required
              label={<span>Call fact*</span>}
              name="call_fact"
            >
              <InputNumber
                disabled
                min={0}
                placeholder="Call fact"
                type="number"
                name="call_fact"
                size="large"
              />
            </Form.Item>
            <Form.Item
              required
              label={<span>Call limit*</span>}
              name="call_limit"
              rules={[
                {
                  required: true,
                  message: "Please input call limit!",
                },
              ]}
            >
              <InputNumber
                min={0}
                placeholder="Call limit"
                type="number"
                name="call_limit"
                size="large"
              />
            </Form.Item>
          </Flex>
          <Form.Item
            required
            label={<span>Active *</span>}
            name="active"
            rules={[{ required: true, message: "Please select user status!" }]}
          >
            <Select
              placeholder="Select user status"
              className={styles.select}
              options={[
                { label: "Active", value: true },
                { label: "Inactive", value: false },
              ]}
              size="large"
            />
          </Form.Item>
          <Form.Item shouldUpdate>
            <Button
              key="submit"
              color="default"
              variant="solid"
              className={styles.createButton}
              onClick={handleSubmit}
              loading={loading}
              disabled={!submittable}
            >
              {initialData ? "Update" : "Create"}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </ConfigProvider>
  );
};

export default UsersModal;
