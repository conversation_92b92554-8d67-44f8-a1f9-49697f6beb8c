"use client";
import { ReactNode, memo, useMemo } from "react";
import "@/styles/globals.scss";
import { NotificationProvider } from "@/context/NotificationContext/NotificationContext";
import { COMPANY_LOGOS } from "@/constants/logos";
import TestimonialCarousel from "@/components/TestimonialCarousel/TestimonialCarousel";
import { TESTIMONIAL_DATA } from "@/constants/testimonial";
import styles from "./layout.module.scss";
import { Box } from "@mui/material";
import PageTransition from "@/app/ui/PageTransition/PageTransition";
const MemoizedTestimonialCarousel = memo(TestimonialCarousel);

const InnerLoginLayout = memo(({ children }: { children: ReactNode }) => {
  return <NotificationProvider>{children}</NotificationProvider>;
});

InnerLoginLayout.displayName = "InnerLoginLayout";

export default function LoginLayout({ children }: { children: ReactNode }) {
  const testimonialProps = useMemo(
    () => ({
      testimonials: TESTIMONIAL_DATA,
      logos: COMPANY_LOGOS,
    }),
    [],
  );

  return (
    <div className={styles.section}>
      <Box className={styles.leftSide}>
        <NotificationProvider>
          <PageTransition>
          {children}
          </PageTransition>
        </NotificationProvider>
      </Box>
      <Box className={styles.rightSide}>
        <MemoizedTestimonialCarousel {...testimonialProps} />
      </Box>
    </div>
  );
}
