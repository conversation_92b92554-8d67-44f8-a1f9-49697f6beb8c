"use client";

import { <PERSON><PERSON>, Config<PERSON><PERSON><PERSON>, Dropdown, Flex, TableColumnsType } from "antd";
import { useGeneralStore } from "@/providers/general-store-provider";
import { useState } from "react";
import styles from "@/app/(dashboard)/metrics/metrics/styles.module.scss";
import cn from "classnames";
import { metricsTableTheme } from "@/app/(dashboard)/metrics/metrics/constants";
import { Table } from "@/components/Table/Table";
import { IPersonality } from "@/types/personality";
import PersonalityModal from "@/app/(dashboard)/simulation/personality/PersonalityModal/PersonalityModal";

export default function PersonalityPage() {
  const { personalities } = useGeneralStore((state) => state);
  const [createPersonalityOpen, setCreatePersonalityOpen] = useState(false);
  const [personalityToEdit, setPersonalityToEdit] =
    useState<IPersonality | null>(null);

  const [loading] = useState(false);

  const columns: TableColumnsType<IPersonality> = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 120,
      render: (id: number) => <b>{id}</b>,
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: 426,
      render: (name: string) => <b>{name}</b>,
    },
    {
      title: "Action",
      key: "action",
      render: (_, item) => (
        <Dropdown
          trigger={["click"]}
          rootClassName={styles.dropdown}
          menu={{
            items: [
              {
                key: "title",
                label: <b className={styles.dropdownTitle}>Actions</b>,
                type: "group",
                children: [
                  { type: "divider" },
                  {
                    className: styles.dropdownItem,
                    key: "edit",
                    label: (
                      <Button
                        variant="link"
                        color="default"
                        icon={<i className={cn(styles.bx, "bx bx-edit")}></i>}
                      >
                        Edit
                      </Button>
                    ),
                    onClick: () => {
                      setPersonalityToEdit(item);
                      setCreatePersonalityOpen(true);
                    },
                  },
                  {
                    className: styles.dropdownItem,
                    key: "delete",
                    label: (
                      <Button
                        variant="link"
                        color="danger"
                        icon={<i className={cn(styles.bx, "bx bx-trash")}></i>}
                      >
                        Delete
                      </Button>
                    ),
                    onClick: () => {},
                  },
                ],
              },
            ],
          }}
        >
          <Button
            shape="default"
            type="text"
            icon={
              <i className={cn(styles.bx, "bx bx-dots-horizontal-rounded")}></i>
            }
          />
        </Dropdown>
      ),
      width: 104,
    },
  ];

  return (
    <Flex vertical gap={24}>
      <Button
        size="large"
        icon={<i className={cn(styles.bx, "bx bx-plus")}></i>}
        color="default"
        variant="solid"
        className={cn(styles.button, styles.addButton)}
        onClick={() => setCreatePersonalityOpen(true)}
        disabled={loading}
      >
        Create Personality
      </Button>
      <ConfigProvider theme={metricsTableTheme}>
        <Table<IPersonality>
          columns={columns}
          loading={loading}
          dataSource={personalities}
          rowKey={(record) => record.id}
        />
      </ConfigProvider>
      {createPersonalityOpen && (
        <PersonalityModal
          onCancel={() => {
            setCreatePersonalityOpen(false);
            setPersonalityToEdit(null);
          }}
          isModalOpen={createPersonalityOpen}
          initialData={personalityToEdit}
        />
      )}
    </Flex>
  );
}
