import {
  <PERSON><PERSON>,
  Config<PERSON><PERSON><PERSON>,
  <PERSON>lex,
  Form,
  Input,
  Modal,
  Tooltip,
} from "antd";
import cn from "classnames";
import styles from "./PersonalityModal.module.scss";
import { generateScenariosModalTheme } from "@/app/(dashboard)/scenarios/components/constants";
import { useEffect, useState } from "react";
import { IPersonality } from "@/types/personality";
import usePersonalityAnalytics from "@/app/(dashboard)/simulation/personality/usePersonalityAnalytics";
import { useGeneralStore } from "@/providers/general-store-provider";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import { IErrorFields, IValidateError } from "@/types/validateError";
import { useDebounce } from "react-use";

interface IPersonalityModalProps {
  onCancel: () => void;
  isModalOpen: boolean;
  initialData: IPersonality | null;
}

const PersonalityModal = ({
  onCancel,
  isModalOpen,
  initialData,
}: IPersonalityModalProps) => {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const personalityAnalytics = usePersonalityAnalytics();
  const [loading] = useState(false);
  const [form] = Form.useForm<IPersonality>();
  const [submittable, setSubmittable] = useState<boolean>(false);
  const values = Form.useWatch([], form);
  const [validationErrors, setValidationErrors] = useState<IErrorFields[]>([]);
  const { currentAgentId } = useGeneralStore((state) => state);
  const [modalOpenedTime, setModalOpenedTime] = useState<number | null>(null);

  useEffect(() => {
    if (isModalOpen) {
      setModalOpenedTime(Date.now());
    }
  }, [isModalOpen]);

  useDebounce(
    () => {
      validationErrors.forEach((fieldError: IErrorFields) => {
        if (
          form.isFieldTouched(
            fieldError.name[0] as keyof Omit<IPersonality, "id">,
          )
        ) {
          generalAnalyticsEvents.trackFormValidationError(
            "PersonalityModal",
            fieldError.name[0],
            fieldError.errors[0],
          );
        }
      });
    },
    2000,
    [validationErrors],
  );

  useEffect(() => {
    if (initialData) {
      setSubmittable(true);
      personalityAnalytics.trackUpdatePersonality();
      return;
    }

    form
      .validateFields({ validateOnly: true })
      .then(() => {
        setValidationErrors([]);
        setSubmittable(true);
      })
      .catch((reason: IValidateError) => {
        setValidationErrors(reason.errorFields);
        if (reason.errorFields.length > 0) {
          setSubmittable(false);
        } else {
          setSubmittable(true);
        }
      });
  }, [form, values]);

  const handleSubmit = async () => {
    personalityAnalytics.trackPersonalityTraitAdjusted(
      currentAgentId,
      "name",
      initialData?.name || "",
      values.name,
    );
  };

  const handleCancel = () => {
    const timeSpent = modalOpenedTime ? Date.now() - modalOpenedTime : 0;
    const fields = form.getFieldsValue();
    const fieldsFilled = Object.values(fields).filter(
      (value) => !!value,
    ).length;
    generalAnalyticsEvents.trackModalCloseButtonClicked(
      "PersonalityModal",
      timeSpent,
      `${fieldsFilled}/${Object.keys(fields).length}`,
    );
    onCancel();
  };

  return (
    <ConfigProvider theme={generateScenariosModalTheme}>
      <Modal
        centered
        title={initialData ? "Update Personality" : "Create Personality"}
        open={isModalOpen}
        onCancel={handleCancel}
        rootClassName={styles.modalRoot}
        footer={null}
        closeIcon={<i className={cn(styles.bx, "bx bx-x")}></i>}
      >
        <Form
          layout="vertical"
          requiredMark={false}
          initialValues={
            initialData || {
              name: "",
              metricType: "",
              metricPrompt: "",
            }
          }
          form={form}
          disabled={loading}
        >
          <Form.Item
            required
            label={
              <Flex gap={8}>
                <span>Personality name*</span>
                <Tooltip
                  placement="right"
                  title="Name of the personality"
                  onOpenChange={(open) =>
                    open &&
                    generalAnalyticsEvents.trackHelpIconClicked(
                      "PersonalityModal",
                      "Personality name",
                    )
                  }
                >
                  <i className={cn("bx bx-info-circle", styles.infoIcon)}></i>
                </Tooltip>
              </Flex>
            }
            name="name"
            rules={[
              {
                required: true,
                message: "Personality name is required",
              },
            ]}
          >
            <Input placeholder="American Male" type="text" name="name" />
          </Form.Item>
          <Form.Item shouldUpdate>
            <Button
              key="submit"
              color="default"
              variant="solid"
              className={styles.createButton}
              onClick={handleSubmit}
              loading={loading}
              disabled={!submittable}
            >
              {initialData ? "Update" : "Create"}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </ConfigProvider>
  );
};

export default PersonalityModal;
