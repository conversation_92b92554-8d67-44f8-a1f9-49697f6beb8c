import { useMemo } from "react";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import { useAuthStore } from "@/stores/auth-store";
import { IScenarioDto } from "@/app/api/types/scenarioDto";
import { IScenario } from "@/types/scenario";
import { usePathname } from "next/navigation";

const useEvaluatorAnalytics = () => {
  const analytics = useRudderStackAnalytics();
  const { user } = useAuthStore((state) => state);
  const pathname = usePathname();

  return useMemo(() => {
    const track = (
      eventName: string,
      payload: Record<string, unknown> = {},
    ) => {
      if (!analytics) return;
      analytics.track(eventName, {
        user_id: user?.id,
        userId: user?.id,
        email: user?.email,
        domain: user?.email.split("@")[1],
        timestamp: new Date().toISOString(),
        app: "test.ai",
        category: payload.category
          ? (payload.category as string)
          : "Simulation",
        page_name: pathname,
        ...payload,
      });
    };

    return {
      trackEvaluatorConfigured: (
        agent_id: string,
        settings_changed: IScenarioDto,
      ) =>
        track("evaluator_configured", {
          agent_id,
          settings_changed,
          category: "Evaluator",
        }),
      trackTestCreationStarted: () =>
        track("Test_Creation_Started", {
          entry_point: "manual",
        }),
      trackTestCreationAbandoned: (error_message?: string) =>
        track("Test_Creation_Abandoned", { error_message }),
      trackUpdateScenario: () => {
        track("Update Scenario");
      },
      trackTestSettingModified: (
        test_id: string,
        previous_values: IScenario,
        new_values: IScenarioDto,
      ) => {
        track("Test_Setting_Modified", {
          test_id,
          previous_values,
          new_values,
        });
      },
      trackTestCreationCompleted: (responseBody: IScenarioDto) => {
        track("Test_Creation_Completed", {
          test_type: `Name: ${responseBody.title}, personality: ${responseBody.personality}, metrics: ${responseBody.metrics.map(
            (metric) => metric.name,
          )}`,
        });
      },
      trackCreateScenario: () => {
        track("Create Scenario");
      },
      trackSimulationRunStarted: (ids: string[] | string) => {
        track("Simulation_Run_Started", {
          test_id: ids,
          execution_type: "manual",
        });
      },
      trackSimulationExecutionFailed: (message: {
        scenario_id: string;
        error_type: string;
        message: string;
      }) => {
        track("Simulation_Execution_Failed", {
          test_id: message.scenario_id,
          error_type: message.error_type,
          failure_reason: message.message,
        });
      },
      trackSimulationExecutionSuccess: (message: { scenario_id: string }) => {
        track("Simulation_Execution_Success", {
          test_id: message.scenario_id,
        });
      },

      trackSimulationInitiated: (agent_id: string, metrics_count: number) => {
        track("simulation_initiated", {
          agent_id,
          metrics_count,
        });
      },

      trackSimulationParameterAdjusted: (
        parameter_name: string,
        old_value: string,
        new_value: string,
      ) => {
        track("simulation_parameter_adjusted", {
          parameter_name,
          old_value,
          new_value,
        });
      },
      trackSimulationStarted: (
        agent_id: string,
        simulation_id: string | string[],
        metrics_included: string[],
      ) => {
        track("simulation_started", {
          agent_id,
          simulation_id,
          metrics_included,
        });
      },
      trackSimulationCompleted: (
        simulation_id: string | string[],
        duration: number,
        metrics_count: number,
        pass_rate: number,
      ) => {
        track("simulation_completed", {
          simulation_id,
          duration,
          metrics_count,
          pass_rate,
        });
      },
    };
  }, [analytics, user?.id]);
};

export default useEvaluatorAnalytics;
