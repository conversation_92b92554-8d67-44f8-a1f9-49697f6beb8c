import { useState, useCallback } from 'react';
import { IMetric } from "@/types/metric";
import { MetricTemplate } from "@/hooks/useMetricTemplates";
import { MetricModalState } from '../types';

export const useMetricsModal = () => {
  const [state, setState] = useState<MetricModalState>({
    isModalOpen: false,
    isTemplateDialogOpen: false,
    selectedTemplate: null,
    editingMetric: null,
  });

  const openCreateModal = useCallback(() => {
    setState(prev => ({
      ...prev,
      isModalOpen: true,
      editingMetric: null,
    }));
  }, []);

  const openEditModal = useCallback((metric: IMetric) => {
    setState(prev => ({
      ...prev,
      isModalOpen: true,
      editingMetric: metric,
    }));
  }, []);

  const openTemplateDialog = useCallback(() => {
    setState(prev => ({
      ...prev,
      isTemplateDialogOpen: true,
    }));
  }, []);

  const closeModal = useCallback(() => {
    setState(prev => ({
      ...prev,
      isModalOpen: false,
      editingMetric: null,
      selectedTemplate: null,
    }));
  }, []);

  const closeTemplateDialog = useCallback(() => {
    setState(prev => ({
      ...prev,
      isTemplateDialogOpen: false,
      selectedTemplate: null,
    }));
  }, []);

  const handleTemplateSelect = useCallback((templates: MetricTemplate[]) => {
    if (templates.length > 0) {
      setState(prev => ({
        ...prev,
        selectedTemplate: templates[0],
        isTemplateDialogOpen: false,
        isModalOpen: true,
      }));
    }
  }, []);

  return {
    ...state,
    openCreateModal,
    openEditModal,
    openTemplateDialog,
    closeModal,
    closeTemplateDialog,
    handleTemplateSelect,
  };
}; 