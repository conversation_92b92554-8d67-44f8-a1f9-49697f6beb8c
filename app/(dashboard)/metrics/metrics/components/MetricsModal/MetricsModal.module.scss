@import '@/styles/variables.module';

.modalRoot {
  .subtitle {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: $gray;
    margin-bottom: 64px;
  }

  :global {
    .ant-modal-close {
      top: 24px !important;
    }

    .ant-form-item-label {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }

    textarea.ant-input {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      padding: 12px;
    }

    .ant-select-selection-placeholder {
      color: #505050 !important;
    }
  }

  .labelSubtitle {
    color: $gray;
    font-weight: 400;
  }

  .infoIcon {
    color: #007AFF;
    font-size: 24px;
  }
}

.bx {
  font-size: 24px;
  cursor: pointer;
  color: $gray;
}

.numberInput {
  width: 100% !important;
}

.createButton {
  width: 100%;
  height: 40px !important;
  padding: 8px 20px !important;
  border-radius: 4px !important;
}

.select {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;

  .selectIcon {
    font-size: 24px;
    color: $black;
  }
}