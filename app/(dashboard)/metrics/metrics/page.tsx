"use client";

import {
  But<PERSON>,
  Dropdown,
} from "antd";
import cn from "classnames";
import styles from "./styles.module.scss";
import { IMetric } from "@/types/metric";
import { useState, useEffect } from "react";
import MetricsModal from "@/app/(dashboard)/metrics/metrics/components/MetricsModal/MetricsModal";
import useMetricManagementAnalytics from "@/app/(dashboard)/metrics/metrics/useMetricManagementAnalytics";
import MetricsTemplateDialog from './components/MetricsTemplateDialog/MetricsTemplateDialog';
import { MetricTemplate } from '@/hooks/useMetricTemplates';
import { Box, Typography, Card, Grid, IconButton, CircularProgress } from '@mui/material';
import AddIcon from "@mui/icons-material/Add";
import { useMetricsAnalytics } from './hooks/useMetricsAnalytics';
import PageTransition from "@/app/ui/PageTransition/PageTransition";
import TemplateHeaderSection from '../../../../components/TemplateHeaderSection/TemplateHeaderSection';
import ExploreMoreButton from '../../../../components/ExploreMoreButton';
import EmptyState from '../../../../components/EmptyState';
import { useAuthStore } from "@/stores/auth-store";
import { useGeneralStore } from "@/providers/general-store-provider";

export default function MetricsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [metricToEdit, setMetricToEdit] = useState<IMetric | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<MetricTemplate | null>(null);
  const {  currentAgentId,  metrics, setMetrics } =
  useGeneralStore((state) => state);

  const { user } = useAuthStore();
  
  const metricManagementAnalytics = useMetricManagementAnalytics();
  const { trackMetricCreated, trackMetricEdited, trackMetricDeleted, trackApiRequestFailed } = useMetricsAnalytics();


  // Simple direct fetch - using store without dependencies
  async function fetchMetrics() {
    if (!user?.token) return Promise.reject("No user token available");
    
    // Set loading state
    setIsLoading(true);
    
    // Get fresh agent ID each time
    
    try {
      const response = await fetch(`/api/agents/${currentAgentId}/metrics`, {
        headers: { Authorization: `Bearer ${user.token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        setMetrics(data.map((metric: any) => ({
          id: String(metric.id),
          name: metric.name,
          metricPrompt: metric.prompt,
          metricType: metric.type,
        })));
        return Promise.resolve();
      } else {
        return Promise.reject(`HTTP error: ${response.status}`);
      }
    } catch (error) {
      console.error("Failed to fetch metrics:", error);
      return Promise.reject(error);
    } finally {
      // Always set loading to false when done
      setIsLoading(false);
    }
  }
  
  // Simple direct delete - using store without dependencies
  async function deleteMetric(metricId: string) {
    if (!user?.token) return;
    
    // Get fresh agent ID each time
    
    try {
      const response = await fetch(`/api/agents/${currentAgentId}/metrics/${metricId}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${user.token}` }
      });
      
      if (response.ok) {
        // Update local state directly
        setMetrics(metrics.filter(metric => metric.id !== metricId));
      }
    } catch (error) {
      console.error("Failed to delete metric:", error);
    }
  }
  
  useEffect(() => {
    fetchMetrics();
  }, [currentAgentId]);
  
  const handleMetricCreated = (metricId: string) => {
    trackMetricCreated(metricId);
    fetchMetrics(); // Refresh
  };
  
  const handleMetricEdited = (metricId: string) => {
    trackMetricEdited(metricId);
    fetchMetrics(); // Refresh
  };
  
  const handleMetricDeleted = async (metricId: string) => {
    try {
      await deleteMetric(metricId);
      trackMetricDeleted(metricId);
    } catch {
      trackApiRequestFailed('Delete Metric', '500', 'Failed to delete metric');
    }
  };

  // Handler for metrics added from template
  const handleMetricsAddedFromTemplate = () => {
    
    // Fetch fresh metrics data (fetchMetrics already sets loading state)
    fetchMetrics()
      .then(() => {
        console.log("Metrics refreshed successfully");
      })
      .catch((error) => {
        console.error("Error refreshing metrics:", error);
      })
      .finally(() => {
        // Always close the dialog
        setIsTemplateDialogOpen(false);
        setSelectedTemplate(null);
      });
  };

  return (
    <PageTransition>
      <Box className={styles.container}>
        <Box className={styles.header}>
          <Typography variant="h4" className={styles.title} sx={{ fontWeight: 800, fontFamily: 'Plus Jakarta Sans' }}>
            Your Metrics
          </Typography>
        </Box>

        <TemplateHeaderSection
          icon="/metrics-icon-template.svg"
          title="Metrics Template"
          button={
            <ExploreMoreButton onClick={() => setIsTemplateDialogOpen(true)}>
              Explore More
            </ExploreMoreButton>
          }
        />

        <Box className={styles.metricsGrid}>
          {isLoading ? (
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column',
              justifyContent: 'center', 
              alignItems: 'center', 
              height: '200px',
              width: '100%' 
            }}>
              <CircularProgress size={40} />
              <Typography sx={{ mt: 2 }}>Loading metrics...</Typography>
            </Box>
          ) : metrics.length === 0 ? (
            <EmptyState
              iconSrc="/task-square-empty.svg"
              iconAlt="Empty Metrics"
              title="No Metrics Found"
              subtitle="Start and create a new one."
              buttonText="Add a new metric"
              onButtonClick={() => setIsModalOpen(true)}
              buttonIcon={<AddIcon sx={{ color: '#fff' }} />}
            />
          ) : (
            <Grid container spacing={3}>
              {/* Create Metric Card */}
              <Grid item xs={12} sm={6} md={4}>
                <Card
                  onClick={() => setIsModalOpen(true)}
                  sx={{
                    height: '100%',
                    borderRadius: '24px !important',
                    padding: '24px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'column',
                    backgroundColor: '#6941C6 !important',
                    backgroundImage: 'url("/Group.svg") !important',
                    backgroundSize: '40% !important',
                    backgroundRepeat: 'no-repeat !important',
                    backgroundPosition: 'right !important',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease-in-out',
                    position: 'relative',
                    overflow: 'hidden',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(127, 86, 217, 0.2)',
                    },
                  }}
                >
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: '100%',
                    justifyContent: 'center',
                    alignItems: 'center',
                    position: 'relative',
                    zIndex: 1,
                  }}>
                    <Box sx={{ textAlign: 'center' }}>
                      <IconButton
                        sx={{
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          borderRadius: '50%',
                          width: 40,
                          height: 40,
                          mb: 2,
                          '&:hover': {
                            backgroundColor: 'rgba(255, 255, 255, 0.2)',
                          },
                        }}
                      >
                        <AddIcon sx={{ color: '#fff', fontSize: 24 }} />
                      </IconButton>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          fontSize: "20px",
                          lineHeight: "30px",
                          color: "white",
                          fontFamily: "Plus Jakarta Sans",
                          mb: 1
                        }}
                      >
                        Create Metric
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "rgba(255, 255, 255, 0.8)",
                          fontSize: "14px",
                          lineHeight: "20px",
                          fontFamily: "Plus Jakarta Sans",
                          maxWidth: "280px"
                        }}
                      >
                        Create a new metric to evaluate your agent&apos;s performance
                      </Typography>
                    </Box>
                  </Box>
                </Card>
              </Grid>
              {/* Existing Metrics */}
              {metrics.map((metric) => (
                <Grid item xs={12} sm={6} md={4} key={metric.id}>
                  <Card
                    className={styles.metricCardContainer}
                    sx={{
                      p: 3,
                      borderRadius: '24px !important',
                      border: '1px solid #E5E7EB',
                      boxShadow: 'none',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      '&:hover': {
                        borderColor: '#7F56D9',
                        backgroundColor: '#F9F5FF',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box>
                        <Typography variant="h6" sx={{ fontFamily: 'Plus Jakarta Sans', fontWeight: 600, mb: 1 }}>
                          {metric.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ fontFamily: 'Plus Jakarta Sans' }}>
                          {metric.metricType}
                        </Typography>
                      </Box>
                      <Dropdown
                        trigger={["click"]}
                        rootClassName={styles.dropdown}
                        onOpenChange={(open) => {
                          if (open) {
                            metricManagementAnalytics.trackMetricActionMenuOpened(
                              metric.id,
                              metric.name,
                            );
                          }
                        }}
                        menu={{
                          items: [
                            {
                              key: "edit",
                              label: "Edit",
                              icon: <i className={cn(styles.bx, "bx bx-edit")} />,
                              onClick: () => {
                                metricManagementAnalytics.trackMetricEditClicked(
                                  metric.id,
                                  0,
                                );
                                setMetricToEdit(metric);
                                setIsModalOpen(true);
                              },
                            },
                            {
                              key: "delete",
                              label: "Delete",
                              icon: <i className={cn(styles.bx, "bx bx-trash")} />,
                              onClick: () => handleMetricDeleted(metric.id),
                            },
                          ],
                        }}
                      >
                        <IconButton size="small" className={styles.actionButton}>
                          <i className={cn(styles.bx, "bx bx-dots-horizontal-rounded")} />
                        </IconButton>
                      </Dropdown>
                    </Box>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        fontFamily: 'Plus Jakarta Sans',
                        flexGrow: 1,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                      }}
                    >
                      {metric.metricPrompt}
                    </Typography>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>

        {/* Add Metric FAB */}
        {metrics.length > 0 && (
          <Box
            sx={{
              position: 'fixed',
              bottom: 24,
              right: 24,
            }}
          >
            <Button
              type="primary"
              onClick={() => setIsModalOpen(true)}
              style={{
                backgroundColor: '#7F56D9',
                borderRadius: '50%',
                width: '56px',
                height: '56px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              icon={<i className="bx bx-plus" style={{ fontSize: '24px' }} />}
            />
          </Box>
        )}

        {/* Modals */}
        <MetricsModal
          onCancel={() => {
            setIsModalOpen(false);
            setMetricToEdit(null);
            setSelectedTemplate(null);
          }}
          isModalOpen={isModalOpen}
          initialData={metricToEdit}
          templateData={selectedTemplate}
          onMetricCreated={handleMetricCreated}
          onMetricEdited={handleMetricEdited}
        />

        <MetricsTemplateDialog
          open={isTemplateDialogOpen}
          onClose={() => {
            setIsTemplateDialogOpen(false);
            setSelectedTemplate(null);
          }}
          onMetricsAdded={handleMetricsAddedFromTemplate}
        />
      </Box>
    </PageTransition>
  );
}
