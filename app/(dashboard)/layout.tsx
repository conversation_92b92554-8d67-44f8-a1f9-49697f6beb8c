"use client";

import "@/styles/globals.scss";
import { ReactNode } from "react";
import DashboardLayout from "@/layouts/DashboardLayout";
import { GeneralStoreProvider } from "@/providers/general-store-provider";
import { notification } from "antd";
import ReactQueryProvider from "@/providers/ReactQueryProvider";
import OnboardingGuide from "@/components/OnboardingGuide/OnboardingGuide";
import AuthGuard from "@/components/Guards/AuthGuard";
import SubscriptionGuard from "@/components/Guards/SubscriptionGuard";
import AgentGuard from "@/components/Guards/AgentGuard";

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const [, contextHolder] = notification.useNotification();

  return (
    <ReactQueryProvider>
      <AuthGuard>
        <SubscriptionGuard>
        <AgentGuard>
          <GeneralStoreProvider>
              <DashboardLayout>{children}</DashboardLayout>
              <OnboardingGuide />
              {contextHolder}
          </GeneralStoreProvider>
        </AgentGuard>
      </SubscriptionGuard>
    </AuthGuard>
    </ReactQueryProvider >
  );
}
